export interface SequenceError extends Error {
}
export interface SequenceErrorCtor {
    /**
     * @deprecated Internal implementation detail. Do not construct error instances.
     * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269
     */
    new (message: string): SequenceError;
}
/**
 * An error thrown when something is wrong with the sequence of
 * values arriving on the observable.
 *
 * @see {@link operators/single}
 */
export declare const SequenceError: SequenceErrorCtor;
//# sourceMappingURL=SequenceError.d.ts.map